import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';
import 'package:upshift/services/remote_config_service.dart';

void main() {
  group('RemoteConfigService', () {
    late RemoteConfigService remoteConfigService;

    setUp(() {
      remoteConfigService = RemoteConfigService.instance;
    });

    group('Singleton Pattern', () {
      test('should return the same instance', () {
        // Act
        final instance1 = RemoteConfigService.instance;
        final instance2 = RemoteConfigService.instance;

        // Assert
        expect(instance1, same(instance2));
      });
    });

    group('Model Selection Logic', () {
      test('should return default model when not initialized', () {
        // Act & Assert - should not throw and return default
        expect(
          () => remoteConfigService.getChatModelForCurrentUser(),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getTitleGenerationModelForCurrentUser(),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getProfileUpdateModelForCurrentUser(),
          returnsNormally,
        );
      });

      test('should handle free subscription correctly', () {
        // Arrange
        final freeSubscription = UserSubscription.free();

        // Act & Assert - should not throw
        expect(
          () => remoteConfigService.getChatModel(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getTitleGenerationModel(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getProfileUpdateModel(
            userSubscription: freeSubscription,
          ),
          returnsNormally,
        );
      });

      test('should handle premium subscription correctly', () {
        // Arrange
        final premiumSubscription = UserSubscription(
          isActive: true,
          entitlements: [SubscriptionEntitlement.premium],
          lastChecked: DateTime.now(),
        );

        // Act & Assert - should not throw
        expect(
          () => remoteConfigService.getChatModel(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getTitleGenerationModel(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getProfileUpdateModel(
            userSubscription: premiumSubscription,
          ),
          returnsNormally,
        );
      });

      test('should handle null subscription gracefully', () {
        // Act & Assert - should not throw
        expect(
          () => remoteConfigService.getChatModel(userSubscription: null),
          returnsNormally,
        );
        expect(
          () => remoteConfigService.getTitleGenerationModel(
            userSubscription: null,
          ),
          returnsNormally,
        );
        expect(
          () =>
              remoteConfigService.getProfileUpdateModel(userSubscription: null),
          returnsNormally,
        );
      });
    });

    group('Error Handling', () {
      test('should handle initialization gracefully', () async {
        // Act & Assert - should not throw
        expect(() => remoteConfigService.initialize(), returnsNormally);
      });

      test('should handle refresh gracefully', () async {
        // Act & Assert - should not throw
        expect(() => remoteConfigService.refresh(), returnsNormally);
      });

      test('should return config values without throwing', () {
        // Act & Assert - should not throw
        expect(() => remoteConfigService.getAllConfigValues(), returnsNormally);

        final configValues = remoteConfigService.getAllConfigValues();
        expect(configValues, isA<Map<String, String>>());
      });
    });

    group('Default Values', () {
      test('should have sensible default model names', () {
        // Act
        final chatModel = remoteConfigService.getChatModelForCurrentUser();
        final titleModel = remoteConfigService
            .getTitleGenerationModelForCurrentUser();
        final profileModel = remoteConfigService
            .getProfileUpdateModelForCurrentUser();

        // Assert
        expect(chatModel, contains('gemini'));
        expect(titleModel, contains('gemini'));
        expect(profileModel, contains('gemini'));

        // Should not be empty
        expect(chatModel.isNotEmpty, true);
        expect(titleModel.isNotEmpty, true);
        expect(profileModel.isNotEmpty, true);
      });
    });
  });
}
